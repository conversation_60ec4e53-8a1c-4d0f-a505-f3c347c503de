<script lang="ts">
    import '../app.css';
    import { currentTheme, darkMode } from '$lib/stores/theme';
    import { authStore } from '$lib/stores/auth';
    import { goto } from '$app/navigation';
    import { browser } from '$app/environment';
    import { onMount } from 'svelte';

    // This ensures the stores are initialized and their side effects (applying theme to document.documentElement) run
    // The actual logic for applying the theme to document.documentElement is within theme.ts
    // Access the stores to trigger their initialization
    void $currentTheme;
    void $darkMode;
    
    // Initialize auth store
    void $authStore;
	
    let { children } = $props();
    
    // Handle redirects for authenticated users after hydration
    
    onMount(() => {
        if (!browser) return;
        
        let unsubscribe: () => void;
        
        unsubscribe = authStore.subscribe((auth) => {
            if (auth.initialized && auth.session) {
                const path = window.location.pathname;
                
                // Handle all authenticated redirects
                if (path.startsWith('/auth/') || path === '/') {
                    goto('/app');
                    unsubscribe?.();
                }
            }
        });
        
        return () => unsubscribe?.();
    });
</script>

<div class="min-h-screen bg-background">
    <header class="sticky top-0 z-50 border-b border-border bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
    </header>
	
    <main>
        {@render children()}
    </main>
</div>
