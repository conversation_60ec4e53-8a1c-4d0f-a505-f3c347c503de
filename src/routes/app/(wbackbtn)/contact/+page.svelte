<script lang="ts">
    import YbdIcon2 from '$lib/components/YbdIcon2.svelte';
</script>

<div class="flex-1 overflow-y-auto p-4">
    <h1 class="text-2xl font-bold text-foreground mb-6">Contact Us</h1>
	
    <p class="text-foreground mb-6 leading-relaxed">
        Have questions or need support? We'd love to hear from you!
    </p>

    <div class="space-y-4 mb-8">
        <a
            href="https://www.upliftingactions.com/contact"
            target="_blank"
            rel="noopener noreferrer"
            class="flex items-center p-4 rounded-lg bg-background border border-border shadow-md hover:bg-muted transition-colors"
        >
            <div class="w-6 h-6 text-primary mr-3">
                <!-- Support icon -->
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            </div>
            <span class="text-foreground text-lg">Contact Support</span>
        </a>
    </div>

    <div class="rounded-lg p-4 border border-primary">
        <h2 class="text-lg font-semibold text-primary mb-2">Get Support</h2>
        <p class="text-foreground mb-2">For all questions and support, please visit our contact form:</p>
        <a href="https://www.upliftingactions.com/contact" class="text-primary hover:underline">
            www.upliftingactions.com/contact
        </a>
    </div>

    <!-- YbdIcon2 at the bottom of the page -->
    <div class="flex justify-center mt-8 mb-4">
        <YbdIcon2 size="96" className="text-primary opacity-60" />
    </div>
</div>