<script lang="ts">
    import { goto } from '$app/navigation';
    import { profile } from '$lib/stores/profile';
    import Button from '$lib/components/Button.svelte';

    function finishWizard() {
        // Mark wizard as completed
        profile.update(p => {
            p.wizardCompleted = true;
            return p;
        });
    
        // Redirect to dashboard
        goto('/app');
    }

    function goBack() {
        goto('/app/wizard/schedule'); // Navigate to the previous step (Schedule)
    }
</script>

<div class="flex flex-col items-center justify-center text-center p-4x">
    <h2 class="text-2xl font-bold mb-4">Moving Forward!</h2>
    <p class="text-lg mb-8">
        You’ve taken another great step towards embracing Your Best Days by completing your profile. Your responses will help generate messages to help you re-sync your focus and beliefs with your goals.
    </p>

    <p class="text-lg mb-8">
        Free yourself from peace of mind thats limited by external triggers. Move past external circumstances dictating your internal experience.
    </p>

    <p class="text-lg mb-8">
        Remember, you can revisit the wizard to update your preferences at any time.
    </p>
    <div class="flex space-x-4">
        <Button on:click={goBack} size="xl" variant="outline">
            Back
        </Button>

        <Button on:click={finishWizard} size="xl">
            Finish
        </Button>
    </div>
</div>