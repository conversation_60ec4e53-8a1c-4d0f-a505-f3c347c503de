import { supabase } from './supabase/supabaseClient';
import { signOut } from '$lib/auth';
import { browser } from '$app/environment';
import { App } from '@capacitor/app';
import { Network } from '@capacitor/network';

/**
 * Service to verify if a user still exists in the database
 * This helps handle cases where users are deleted from Supabase admin
 * Works with both web and Capacitor native apps
 */

let verificationInterval: NodeJS.Timeout | null = null;
let lastVerificationTime = 0;
const VERIFICATION_INTERVAL = 5 * 60 * 1000; // 5 minutes
const MIN_TIME_BETWEEN_CHECKS = 30 * 1000; // 30 seconds minimum between checks

/**
 * Check if the device is online
 * Works for both web and Capacitor environments
 */
async function isOnline(): Promise<boolean> {
    try {
        // Try Capacitor Network plugin first (for native apps)
        const status = await Network.getStatus();
        return status.connected;
    } catch (error) {
        // Fallback to navigator.onLine for web
        return navigator.onLine;
    }
}

/**
 * Check if the current user still exists in the database
 * @returns true if user exists, false if not
 */
export async function verifyUserExists(): Promise<boolean> {
    try {
        // Get current session
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session?.user?.id) {
            return false;
        }

        // Check if user exists in auth.users table
        // We use the service role client or RLS policies should allow reading own user data
        const { data, error } = await supabase
            .from('profiles')
            .select('id')
            .eq('id', session.user.id)
            .single();

        if (error) {
            // If we get a PGRST116 error (no rows returned), user doesn't exist
            if (error.code === 'PGRST116') {
                console.warn('User profile not found in database');
                return false;
            }
            // For other errors, we'll assume user exists to avoid false positives
            console.error('Error checking user existence:', error);
            return true;
        }

        return !!data;
    } catch (error) {
        console.error('Error in verifyUserExists:', error);
        // On error, assume user exists to avoid false positives
        return true;
    }
}

/**
 * Handle the case when a user is found to be deleted
 */
async function handleDeletedUser() {
    console.warn('User has been deleted from database, signing out...');
    
    try {
        // Sign out the user
        await signOut();
    } catch (error) {
        console.error('Error signing out deleted user:', error);
        // Force reload to clear any cached state
        if (browser) {
            window.location.href = '/auth/signin';
        }
    }
}

/**
 * Perform a user verification check if online
 */
export async function checkUserIfOnline() {
    // Only run in browser
    if (!browser) return;
    
    // Check if online
    const online = await isOnline();
    if (!online) {
        console.log('Skipping user verification - offline');
        return;
    }
    
    // Throttle checks to avoid too frequent verification
    const now = Date.now();
    if (now - lastVerificationTime < MIN_TIME_BETWEEN_CHECKS) {
        return;
    }
    
    lastVerificationTime = now;
    
    const userExists = await verifyUserExists();
    if (!userExists) {
        await handleDeletedUser();
    }
}

/**
 * Start periodic user verification checks
 */
export function startPeriodicVerification() {
    if (!browser) return;
    
    // Clear any existing interval
    stopPeriodicVerification();
    
    // Initial check
    checkUserIfOnline();
    
    // Set up periodic checks
    verificationInterval = setInterval(() => {
        checkUserIfOnline();
    }, VERIFICATION_INTERVAL);
    
    // Set up network status listener for Capacitor
    setupNetworkListener();
}

/**
 * Stop periodic user verification checks
 */
export function stopPeriodicVerification() {
    if (verificationInterval) {
        clearInterval(verificationInterval);
        verificationInterval = null;
    }
    
    // Remove network listener
    if (browser) {
        try {
            Network.removeAllListeners();
        } catch (error) {
            // Ignore if Capacitor is not available
        }
    }
}

/**
 * Set up network status listener
 */
async function setupNetworkListener() {
    try {
        // Listen for network status changes in Capacitor
        await Network.addListener('networkStatusChange', (status) => {
            if (status.connected) {
                console.log('Network is back online, checking user status...');
                // Add a small delay to ensure network is fully available
                setTimeout(() => {
                    checkUserIfOnline();
                }, 1000);
            }
        });
    } catch (error) {
        // Fallback for web - listen to online event
        window.addEventListener('online', () => {
            console.log('Network is back online, checking user status...');
            setTimeout(() => {
                checkUserIfOnline();
            }, 1000);
        });
    }
}

/**
 * Set up app state change listeners for verification
 * Works for both web and Capacitor environments
 */
export async function setupAppStateListeners() {
    if (!browser) return;
    
    try {
        // For Capacitor apps - listen to app state changes
        await App.addListener('appStateChange', (state: { isActive: boolean }) => {
            if (state.isActive) {
                console.log('App became active, checking user status...');
                checkUserIfOnline();
            }
        });
        
        // Also listen for app resume (Android specific)
        await App.addListener('resume', () => {
            console.log('App resumed, checking user status...');
            checkUserIfOnline();
        });
    } catch (error) {
        // Fallback for web - use visibility change and focus events
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                console.log('Page became visible, checking user status...');
                checkUserIfOnline();
            }
        });
        
        window.addEventListener('focus', () => {
            console.log('Window focused, checking user status...');
            checkUserIfOnline();
        });
    }
}

/**
 * Initialize user verification service
 * Call this once when the app starts
 */
export function initializeUserVerification() {
    if (!browser) return;
    
    // Set up app state listeners
    setupAppStateListeners();
    
    // Start periodic verification
    startPeriodicVerification();
}

/**
 * Clean up user verification service
 * Call this when the app is closing or user logs out
 */
export function cleanupUserVerification() {
    stopPeriodicVerification();
    
    if (browser) {
        try {
            // Remove Capacitor listeners
            App.removeAllListeners();
            Network.removeAllListeners();
        } catch (error) {
            // Ignore if Capacitor is not available
        }
    }
}