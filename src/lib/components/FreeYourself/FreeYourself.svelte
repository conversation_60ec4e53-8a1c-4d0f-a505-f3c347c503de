<script lang="ts">
    import PremiumPrompt from '$lib/components/PremiumPrompt.svelte';
        import { emotions as emotionsData, type Response } from './emotionsData';
    import EmotionGrid from './EmotionGrid.svelte';
    import ResponseDisplay from './ResponseDisplay.svelte';
    
    // Add action handlers to the imported emotions
    const emotions = emotionsData.map(emotion => ({
        ...emotion,
        action: () => handleEmotionSelect(emotion.label)
    }));
    
    let selectedEmotion: string | null = null;
    let showResponse = false;
    let currentResponseIndex = 0;
    
    // Make current response reactive - depends on currentResponseIndex and selectedEmotion
    $: currentResponse = selectedEmotion && showResponse ? getSelectedResponses()[currentResponseIndex] : null;
    
    function handleEmotionSelect(emotion: string) {
        selectedEmotion = emotion;
        showResponse = true;
        currentResponseIndex = Math.floor(Math.random() * getSelectedResponses().length);
    }
    
    function resetSelection() {
        selectedEmotion = null;
        showResponse = false;
        currentResponseIndex = 0;
    }
    
    function getSelectedResponses(): Response[] {
        const emotion = emotions.find(e => e.label === selectedEmotion);
        return emotion?.responses || [];
    }
    
    
    function showNextResponse() {
        const responses = getSelectedResponses();
        if (responses.length > 1) {
            let newIndex;
            do {
                newIndex = Math.floor(Math.random() * responses.length);
            } while (newIndex === currentResponseIndex);
            currentResponseIndex = newIndex;
        }
    }
    
</script>

<div class="text-center">
    <p class="text-foreground/80 mb-8 max-w-2xl mx-auto">
        Help remind yourself of <span class="underline">your power to free yourself</span> in any moment, and realign your focus with the opportunities in this moment.
    </p>

    <p class="text-foreground/80 mb-8 max-w-2xl mx-auto">
        Choose what's bothering you and see ideas for freedom in any situation.
    </p>
    
    {#if !showResponse}
        <!-- Emotion Selection Grid -->
        <EmotionGrid {emotions} onSelect={handleEmotionSelect} />
    {:else}
        <!-- Response Section - Fullscreen -->
        <ResponseDisplay 
            {currentResponse}
            onNextClick={showNextResponse}
            onReset={resetSelection}
        />
    {/if}
    
    <!-- Premium Prompt -->
    <PremiumPrompt />
</div>