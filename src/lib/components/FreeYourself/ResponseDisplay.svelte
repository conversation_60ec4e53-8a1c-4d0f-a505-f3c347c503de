<script lang="ts">
    import type { Response } from './emotionsData';
    import ResponseHeader from './ResponseHeader.svelte';
    // import LikeButton from './LikeButton.svelte';
    
    export let currentResponse: Response | null;
    export let onNextClick: () => void;
    export let onReset: () => void;
</script>

<div class="fixed inset-0 bg-primary-400 z-50 overflow-y-auto safe-area-inset">
    <div class="min-h-full flex items-center justify-center py-4 px-6 pt-safe pb-safe">
        <div class="w-full max-w-2xl">
            <h4 class="font-semibold text-background text-center">Remember Your Power</h4>
            <h6 class="text-background italic mb-6">- to free yourself -</h6>
    
            {#if currentResponse}
                <div>
                    <ResponseHeader responseType={currentResponse.responseType} />
                    
                    <div class="relative">
                        <p class="text-background text-2xl leading-relaxed mb-0 text-center">
                            {currentResponse.content}
                        </p>
                    </div>
                    
                    <div class="flex justify-center mt-8">
                        <button
                            on:click={onNextClick}
                            class="w-32 h-32 rounded-full bg-blue-500 hover:bg-blue-600 active:bg-blue-700 
                                   text-white font-medium transition-colors duration-200 
                                   flex items-center justify-center shadow-lg"
                        >
                            <span class="text-lg">Shift</span>
                        </button>
                    </div>
                </div>
            {/if}
    
            {#if true}
                <div class="pt-6 mt-8 border-t border-background/20">
                    <p class="text-foreground italic mb-6 text-center">
                        "You are not your situation or thoughts. You are the observer. You have the power to choose your focus, beliefs and actions."
                    </p>
                    
                    <div class="flex items-center justify-center gap-4">
                        <!-- <LikeButton /> -->
                        <button
                            on:click={onReset}
                            class="px-8 py-3 bg-background text-primary text-xl rounded-lg hover:bg-background/90 transition-colors font-medium"
                        >
                            Back
                        </button>
                    </div>
                </div>
            {/if}
        </div>
    </div>
</div>

<style>
    .safe-area-inset {
        padding-top: env(safe-area-inset-top, 0);
        padding-bottom: env(safe-area-inset-bottom, 0);
        padding-left: env(safe-area-inset-left, 0);
        padding-right: env(safe-area-inset-right, 0);
    }
    
    .pt-safe {
        padding-top: max(1rem, env(safe-area-inset-top, 1rem));
    }
    
    .pb-safe {
        padding-bottom: max(1rem, env(safe-area-inset-bottom, 1rem));
    }
    
    @supports (padding: max(0px)) {
        .safe-area-inset {
            padding-top: max(env(safe-area-inset-top, 0), 20px);
            padding-bottom: max(env(safe-area-inset-bottom, 0), 20px);
        }
    }
</style>