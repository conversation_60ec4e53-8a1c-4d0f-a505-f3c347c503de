<script lang="ts">
    export let title: string;
    export let price: string;
    export let recommended: boolean = false;
    
/**
     * <PERSON><PERSON> temporarily commented out to fix svelte-check warnings.
     * These are passed by parent components but not yet used in the template.
     * 
     * Future use cases:
     * - subInterval & subIntervalType: For displaying billing periods (e.g., "per month", "per 3 months")
     * 
     * When implementing checkout functionality, uncomment these:
     */
    export let priceId: string;
    export let subInterval: number;
    export let subIntervalType: 'yr' | 'mo';
</script>

<div class="bg-white rounded-lg shadow-lg p-6 relative {recommended ? 'ring-2 ring-blue-500' : ''}">
    {#if recommended}
        <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                Recommended
            </span>
        </div>
    {/if}
    
    <div class="text-center mb-6">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{title}</h3>
        <div class="text-3xl font-bold text-blue-600 mb-2">
            ${price}
        </div>
    </div>
    
    <div class="price-card-body">
        <slot />
    </div>
</div>

<style>
    .price-card-body :global(ul) {
        margin-bottom: 1.5rem;
    }
</style>